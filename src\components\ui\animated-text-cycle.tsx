import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface AnimatedTextCycleProps {
  words: string[];
  interval?: number;
  className?: string;
}

export default function AnimatedTextCycle({
  words,
  interval = 5000,
  className = "",
}: AnimatedTextCycleProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [maxWidth, setMaxWidth] = useState("auto");
  const measureRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLSpanElement>(null);

  // Calculate the maximum width of all words and update on resize
  const updateMaxWidth = () => {
    if (measureRef.current) {
      const elements = measureRef.current.children;
      let widestWidth = 0;

      // Find the widest word
      for (let i = 0; i < elements.length; i++) {
        const rect = elements[i].getBoundingClientRect();
        widestWidth = Math.max(widestWidth, rect.width);
      }

      // Add a small buffer (10px) to prevent any potential text cutoff
      setMaxWidth(`${widestWidth + 10}px`);
    }
  };

  // Initial calculation and resize handler
  useEffect(() => {
    updateMaxWidth();

    // Add resize listener to recalculate on window size changes
    const handleResize = () => {
      updateMaxWidth();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [words]); // Recalculate if words array changes

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, interval);

    return () => clearInterval(timer);
  }, [interval, words.length]);

  // Container animation for the whole word
  const containerVariants = {
    hidden: {
      y: -10,
      opacity: 0,
      filter: "blur(8px)"
    },
    visible: {
      y: 0,
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    },
    exit: {
      y: 10,
      opacity: 0,
      filter: "blur(8px)",
      transition: {
        duration: 0.4,
        ease: "easeIn"
      }
    },
  };

  return (
    <>
      {/* Hidden measurement div with all words rendered */}
      <div
        ref={measureRef}
        aria-hidden="true"
        className="absolute opacity-0 pointer-events-none"
        style={{ visibility: "hidden" }}
      >
        {words.map((word, i) => (
          <span key={i} className={`font-bold ${className}`}>
            {word}
          </span>
        ))}
      </div>

      {/* Visible animated word with fixed width */}
      <motion.span
        ref={containerRef}
        className="relative inline-block align-bottom"
        style={{
          width: maxWidth,
          display: "inline-block",
          verticalAlign: "baseline",
          margin: "0 0.1em"
        }}
      >
        <AnimatePresence mode="wait" initial={false}>
          <motion.span
            key={currentIndex}
            className={`inline-block font-bold ${className}`}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              whiteSpace: "nowrap",
              display: "block"
            }}
          >
            {words[currentIndex]}
          </motion.span>
        </AnimatePresence>
      </motion.span>
    </>
  );
}
