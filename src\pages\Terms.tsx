
import React from "react";

const Terms = () => {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-8 text-gray-800">Terms of Service</h1>

        <div className="prose max-w-none">
          <p className="text-sm text-gray-500 mb-6">Last Updated: May 2024</p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">1. Introduction</h2>
          <p className="mb-6">
            Welcome to Sadaad ("we," "our," "us," "the App," or "the developer"). By downloading, accessing, or using our mobile application, you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, please do not use our App.
          </p>
          <p className="mb-6">
            These Terms constitute a legally binding agreement between you and the developer of Sadaad regarding your use of the App. Please read them carefully.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">2. Account Registration</h2>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">2.1 Account Creation</h3>
          <p className="mb-4">To use certain features of the App, you must create an account. When you create an account, you agree to:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Provide accurate and complete information</li>
            <li>Maintain the security of your account credentials</li>
            <li>Accept responsibility for all activities that occur under your account</li>
            <li>Notify us immediately of any unauthorized use of your account</li>
          </ul>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">2.2 Account Requirements</h3>
          <p className="mb-6">
            You must be at least 13 years old to create an account. By creating an account, you represent that you are at least 13 years old.
          </p>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">2.3 Account Termination</h3>
          <p className="mb-6">
            We reserve the right to suspend or terminate your account at our discretion if you violate these Terms or engage in any activity that we determine to be harmful to the App or other users.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">3. Subscription Plans</h2>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">3.1 Free and Premium Plans</h3>
          <p className="mb-6">
            The App offers both free and premium subscription plans. The free plan provides limited access to features, while premium plans offer additional features and benefits.
          </p>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">3.2 Payment Terms</h3>
          <p className="mb-4">If you choose a premium subscription plan:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>You agree to pay all fees associated with your selected plan</li>
            <li>Payments will be processed through our payment processor, Razorpay</li>
            <li>Subscription fees are charged as a one-time payment for the selected subscription period</li>
            <li>Prices are subject to change, but changes will not affect already purchased subscriptions</li>
          </ul>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">3.3 Subscription Duration</h3>
          <p className="mb-4">Premium subscriptions are currently set for fixed durations:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Once purchased, a premium subscription remains active for its full term</li>
            <li>Premium features are available for the entire duration of the subscription period</li>
            <li>At the end of the subscription period, your account will revert to the free plan unless you purchase a new subscription</li>
          </ul>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">3.4 Refunds and Cancellations</h3>
          <p className="mb-6">
            <strong>No Refunds Policy:</strong> All subscription purchases are final and non-refundable. Once a subscription is purchased, we do not offer refunds or cancellations for any reason, including but not limited to:
          </p>
          <ul className="list-disc ml-6 mb-4">
            <li>Technical issues or compatibility problems</li>
          </ul>
          <p className="mb-6">
            By purchasing a subscription, you acknowledge and agree to this no-refund policy. We encourage users to carefully consider their purchase decision before completing a transaction.
          </p>
          <p className="mb-6">
            If you have questions about our subscription offerings before making a purchase, please contact our customer support <NAME_EMAIL>.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">4. Acceptable Use</h2>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">4.1 Prohibited Activities</h3>
          <p className="mb-4">When using the App, you agree not to:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Use the App for any illegal purpose</li>
            <li>Attempt to gain unauthorized access to the App or other users' accounts</li>
            <li>Upload or transmit viruses, malware, or other harmful code</li>
            <li>Interfere with or disrupt the App's functionality</li>
            <li>Engage in any activity that could disable, overburden, or impair the App</li>
            <li>Use the App to harass, abuse, or harm others</li>
            <li>Attempt to reverse engineer, decompile, or disassemble the App</li>
            <li>Use the App in any manner that could damage or disparage Sadaad</li>
          </ul>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">4.2 Islamic Content Guidelines</h3>
          <p className="mb-4">When using the Islamic chatbot and other religious features:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Understand that the content is for informational purposes only</li>
            <li>Recognize that the App is not a substitute for professional religious guidance</li>
            <li>Use the features respectfully and in accordance with Islamic principles</li>
            <li>Do not use the App to spread misinformation about Islamic teachings</li>
          </ul>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">5. Intellectual Property</h2>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">5.1 App Ownership</h3>
          <p className="mb-6">
            The App, including its content, features, and functionality, is owned by the developer (Muhammed Falah) and is protected by applicable copyright and intellectual property laws. The name "Sadaad" and the app's content are the developer's intellectual property, though they may not be formally registered as trademarks.
          </p>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">5.2 Limited License</h3>
          <p className="mb-6">
            We grant you a limited, non-exclusive, non-transferable, revocable license to use the App for your personal, non-commercial use.
          </p>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">5.3 User Data</h3>
          <p className="mb-6">
            The App collects certain data from your usage, such as quiz responses, bookmarked messages, and emotion tracking data. While this data relates to you, you authorize us to store and use this data to provide the App's services, improve functionality, and maintain your progress and preferences. We will handle all such data in accordance with our Privacy Policy.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">6. Disclaimers</h2>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">6.1 Religious Content Disclaimer</h3>
          <p className="mb-4">The Islamic content provided through the App, including the chatbot responses, duas, and educational materials:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Is for informational and educational purposes only</li>
            <li>Is not a substitute for guidance from qualified Islamic scholars</li>
            <li>May not be applicable to all situations or interpretations</li>
            <li>Should be verified with trusted Islamic sources</li>
          </ul>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">6.2 General Disclaimer</h3>
          <p className="mb-6">
            THE APP IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED. TO THE FULLEST EXTENT PERMITTED BY LAW, WE DISCLAIM ALL WARRANTIES, INCLUDING IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">7. Limitation of Liability</h2>
          <p className="mb-6">
            TO THE MAXIMUM EXTENT PERMITTED BY LAW, THE DEVELOPER OF SADAAD SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, OR ANY LOSS OF PROFITS OR REVENUES, WHETHER INCURRED DIRECTLY OR INDIRECTLY, OR ANY LOSS OF DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES RESULTING FROM:
          </p>
          <ul className="list-disc ml-6 mb-4">
            <li>YOUR USE OR INABILITY TO USE THE APP</li>
            <li>ANY UNAUTHORIZED ACCESS TO OR USE OF OUR SERVERS AND/OR ANY PERSONAL INFORMATION STORED THEREIN</li>
            <li>ANY INTERRUPTION OR CESSATION OF TRANSMISSION TO OR FROM THE APP</li>
            <li>ANY BUGS, VIRUSES, TROJAN HORSES, OR THE LIKE THAT MAY BE TRANSMITTED TO OR THROUGH THE APP</li>
          </ul>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">8. Indemnification</h2>
          <p className="mb-6">
            You agree to defend, indemnify, and hold harmless the developer of Sadaad from and against any claims, liabilities, damages, losses, and expenses, including reasonable attorneys' fees, arising out of or in any way connected with your access to or use of the App, your violation of these Terms, or your violation of any rights of another.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">9. Modifications to Terms</h2>
          <p className="mb-6">
            We may modify these Terms at any time by posting the revised Terms on the App. Your continued use of the App after the posting of the revised Terms constitutes your acceptance of the changes. It is your responsibility to check the Terms periodically for changes.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">10. Governing Law</h2>
          <p className="mb-6">
            These Terms shall be governed by and construed in accordance with the laws of India, without regard to its conflict of law provisions.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">11. Dispute Resolution</h2>
          <p className="mb-6">
            Any dispute arising from or relating to these Terms or your use of the App shall first be resolved through good-faith negotiations. If such negotiations fail, the dispute shall be resolved through arbitration in accordance with the Arbitration and Conciliation Act, 1996 of India.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">12. Severability</h2>
          <p className="mb-6">
            If any provision of these Terms is found to be unenforceable or invalid, that provision shall be limited or eliminated to the minimum extent necessary so that the Terms shall otherwise remain in full force and effect and enforceable.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">13. Contact Information</h2>
          <p className="mb-6">
            If you have any questions about these Terms, please contact us at:
          </p>
          <p className="mb-4">Email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
        </div>
      </div>
    </div>
  );
};

export default Terms;
