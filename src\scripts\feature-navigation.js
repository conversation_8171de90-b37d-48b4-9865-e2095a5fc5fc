// Feature navigation functionality
document.addEventListener('DOMContentLoaded', () => {
  // This function will be called after <PERSON><PERSON> has rendered the components
  setTimeout(() => {
    initializeFeatureNavigation();
  }, 500);
});

function initializeFeatureNavigation() {
  console.log("Initializing feature navigation");

  // Get all feature containers
  const featureContainers = document.querySelectorAll('.feature-container');
  console.log(`Found ${featureContainers.length} feature containers`);

  featureContainers.forEach((container, containerIndex) => {
    const frames = container.querySelectorAll('.feature-frame');
    const dots = container.querySelectorAll('.frame-dot');
    const prevButton = container.querySelector('.frame-arrow:first-child');
    const nextButton = container.querySelector('.frame-arrow:last-child');

    console.log(`Container ${containerIndex}: ${frames.length} frames, ${dots.length} dots`);

    // Store the current frame index in a data attribute on the container
    if (!container.dataset.currentFrameIndex) {
      container.dataset.currentFrameIndex = "0";
    }

    // Function to show a specific frame
    const showFrame = (index) => {
      console.log(`Showing frame ${index} in container ${containerIndex}`);

      // Hide all frames
      frames.forEach(frame => {
        frame.style.display = 'none';
      });

      // Deactivate all dots
      dots.forEach(dot => {
        dot.classList.remove('active');
      });

      // Show the selected frame and activate the corresponding dot
      frames[index].style.display = 'flex';
      dots[index].classList.add('active');

      // Update current index
      container.dataset.currentFrameIndex = index.toString();
    };

    // Add click event listeners to dots
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        showFrame(index);
      });
    });

    // Add click event listeners to navigation arrows
    if (prevButton) {
      prevButton.addEventListener('click', () => {
        const currentIndex = parseInt(container.dataset.currentFrameIndex || "0");
        let newIndex = currentIndex - 1;
        if (newIndex < 0) {
          newIndex = frames.length - 1; // Loop back to the last frame
        }
        showFrame(newIndex);
      });
    }

    if (nextButton) {
      nextButton.addEventListener('click', () => {
        const currentIndex = parseInt(container.dataset.currentFrameIndex || "0");
        let newIndex = currentIndex + 1;
        if (newIndex >= frames.length) {
          newIndex = 0; // Loop back to the first frame
        }
        showFrame(newIndex);
      });
    }

    // Initialize the first frame
    showFrame(0);
  });
}

// Also initialize when the Features page is loaded
document.addEventListener('route-change', (event) => {
  console.log("Route changed:", event.detail);
  if (event.detail && event.detail.pathname === "/features") {
    console.log("Features page loaded, initializing navigation");
    setTimeout(initializeFeatureNavigation, 300);
  }
});

// Re-initialize on window resize
window.addEventListener('resize', () => {
  setTimeout(initializeFeatureNavigation, 300);
});
