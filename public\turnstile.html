<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Turnstile Verification</title>
  <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background: #f9f9f9;
      font-family: sans-serif;
    }
    .container {
      max-width: 400px;
      width: 100%;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="container">
    
    <div class="cf-turnstile"
         data-sitekey="0x4AAAAAABqrF3b38wdxEczo"
         data-callback="onTurnstileSuccess">
    </div>
  </div>

  <script>
    function onTurnstileSuccess(token) {
      window.parent.postMessage(token, "*");
    }
  </script>
</body>
</html>
