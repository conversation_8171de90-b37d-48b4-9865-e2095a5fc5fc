import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

const RouteChangeListener = () => {
  const location = useLocation();
  const prevLocation = useRef(location.pathname);

  useEffect(() => {
    if (location.pathname !== prevLocation.current) {
      prevLocation.current = location.pathname;
      
      // Dispatch a custom event when the route changes
      const event = new CustomEvent('route-change', { 
        detail: { 
          pathname: location.pathname 
        } 
      });
      document.dispatchEvent(event);
    }
  }, [location]);

  return null;
};

export default RouteChangeListener;
