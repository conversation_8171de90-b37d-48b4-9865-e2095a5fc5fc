
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { BookMarked, MessageSquare, Calendar, Star, <PERSON><PERSON><PERSON>, BookOpen } from "lucide-react";
import ScrollReveal from "@/components/ScrollReveal";
import BackgroundPattern from "@/components/BackgroundPattern";
import AnimatedTextCycle from "@/components/ui/animated-text-cycle";

// Import preview images
import emotionControle from "../assets/images/preview/emotion-controle.png";
import chatbot from "../assets/images/preview/chatbot.png";
import quiz from "../assets/images/preview/quiz.png";
import bookmark from "../assets/images/preview/bookmark.png";
import dua from "../assets/images/preview/dua.png";
import emotionOverview from "../assets/images/preview/emotion-overview.png";
import quizHistory from "../assets/images/preview/quiz-history.png";

// Import hero image
import heroImage from "../assets/images/hero/hero.png";

const Index = () => {
  return (
    <>
      {/* Hero Section */}
      <section className="relative hero-gradient py-16 md:py-24 overflow-hidden before:absolute before:inset-0 before:bg-[url('/src/assets/grain.svg')] before:opacity-[0.05] before:bg-repeat before:mix-blend-overlay">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-8">
                <span className="block sm:inline tracking-tight text-gray-800">Master your </span>
                <AnimatedTextCycle
                  words={["emotions", "anger", "sadness", "fear", "anxiety"]}
                  interval={3000}
                  className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary font-extrabold"
                />
                <span className="block sm:inline tracking-tight text-gray-800"> with </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/90 block sm:inline">Qur'an & Sunnah</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-10 max-w-lg mx-auto lg:mx-0 leading-relaxed">
                Sadaad helps Muslims stay grounded and spiritually strong by managing emotions like anger, fear, sadness, and stress through authentic Islamic guidance.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start relative z-10">
                <Button
                  asChild
                  size="lg"
                  className="rounded-full bg-gradient-to-r from-primary to-secondary hover:shadow-lg transition-all duration-300 hover:scale-105 shadow-md relative overflow-hidden group"
                >
                  <a href="#download" id="download" className="flex items-center gap-2 button-shine">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 15L12 3M12 15L8 11M12 15L16 11M21 15V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Download Now
                  </a>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="rounded-full border-secondary/30 hover:border-secondary hover:bg-secondary/5 transition-all duration-300 hover:scale-105 shadow-sm text-secondary relative z-[20]"
                >
                  <Link to="/features" className="flex items-center gap-2">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    See Features
                  </Link>
                </Button>
              </div>
            </div>
            <div className="lg:w-1/2 flex justify-center">
              <div className="relative animate-float">
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-accent rounded-full opacity-70"></div>
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-secondary/20 rounded-full"></div>
                <img
                  src={heroImage}
                  alt="Sadaad App Preview"
                  className="relative z-10 w-[320px] md:w-[500px] h-auto rounded-3xl"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="hidden md:block absolute top-20 left-12 w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/5 backdrop-blur-md"></div>
        <div className="hidden md:block absolute bottom-12 right-24 w-24 h-24 rounded-full bg-gradient-to-tr from-secondary/30 to-secondary/10 backdrop-blur-md"></div>
        <div className="hidden md:block absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-secondary/10 blur-3xl"></div>
        <div className="hidden md:block absolute bottom-1/3 right-1/3 w-40 h-40 rounded-full bg-primary/5 blur-3xl"></div>
      </section>

      {/* Feature Highlights */}
      <section className="py-16 md:py-24 bg-white relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-[10%] w-64 h-64 rounded-full bg-accent/30 blur-3xl opacity-30"></div>
          <div className="absolute bottom-20 right-[10%] w-80 h-80 rounded-full bg-primary/20 blur-3xl opacity-20"></div>
        </div>
        <BackgroundPattern className="text-primary" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="section-title text-center relative inline-block group">
              Key Features
              <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-primary rounded-full transition-all duration-300 group-hover:w-32 group-hover:bg-gradient-to-r from-primary to-secondary"></span>
            </h2>
            <p className="section-subtitle text-center mt-6">
              Designed to help you manage emotions through Islamic wisdom and practical steps
            </p>
          </div>

          <div className="features-grid">
            {/* Feature Card 1 */}
            <ScrollReveal className="delay-100">
              <div className="feature-card" tabIndex={0}>
                <div className="feature-icon-wrapper">
                  <div className="feature-icon feature-icon-pulse">
                    <MessageSquare className="w-6 h-6" />
                  </div>
                </div>
                <h3>Instant Emotional Control</h3>
                <p>
                  A panic-style button that instantly shows calming steps, Qur'anic verses, duas, and hadith for immediate relief.
                </p>
              </div>
            </ScrollReveal>

            {/* Feature Card 2 */}
            <ScrollReveal className="delay-200">
              <div className="feature-card" tabIndex={0}>
                <div className="feature-icon-wrapper">
                  <div className="feature-icon feature-icon-pulse">
                    <MessageSquare className="w-6 h-6" />
                  </div>
                </div>
                <h3>Islamic Chatbot</h3>
                <p>
                  Ask questions and get responses based on authentic Islamic knowledge with proper citations from trusted sources.
                </p>
              </div>
            </ScrollReveal>

            {/* Feature Card 3 */}
            <ScrollReveal className="delay-300">
              <div className="feature-card" tabIndex={0}>
                <div className="feature-icon-wrapper">
                  <div className="feature-icon feature-icon-pulse">
                    <Calendar className="w-6 h-6" />
                  </div>
                </div>
                <h3>Daily Quiz</h3>
                <p>
                  Fun, bite-sized Islamic quizzes to test your knowledge and deepen your understanding of emotional wellbeing in Islam.
                </p>
              </div>
            </ScrollReveal>

            {/* Feature Card 4 */}
            <ScrollReveal className="delay-400">
              <div className="feature-card" tabIndex={0}>
                <div className="feature-icon-wrapper">
                  <div className="feature-icon feature-icon-pulse">
                    <BookMarked className="w-6 h-6" />
                  </div>
                </div>
                <h3>Bookmark Messages</h3>
                <p>
                  Save helpful chats and guidance from the chatbot for easy reference and create your personal library of wisdom.
                </p>
              </div>
            </ScrollReveal>

            {/* Feature Card 5 */}
            <ScrollReveal className="delay-500">
              <div className="feature-card" tabIndex={0}>
                <div className="feature-icon-wrapper">
                  <div className="feature-icon feature-icon-pulse">
                    <BookOpen className="w-6 h-6" />
                  </div>
                </div>
                <h3>Daily Dua</h3>
                <p>
                  Receive a new dua each day with streak tracking and achievements to build a consistent spiritual practice.
                </p>
              </div>
            </ScrollReveal>

            {/* Feature Card 6 */}
            <ScrollReveal className="delay-600">
              <div className="feature-card" tabIndex={0}>
                <div className="feature-icon-wrapper">
                  <div className="feature-icon feature-icon-pulse">
                    <PieChart className="w-6 h-6" />
                  </div>
                </div>
                <h3>Emotion Journey</h3>
                <p>
                  Track your emotional patterns and receive personalized insights based on Islamic psychology principles.
                </p>
              </div>
            </ScrollReveal>
          </div>

          <ScrollReveal className="delay-700">
            <div className="text-center mt-16">
              <Button asChild size="lg" className="rounded-full shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-primary to-primary/90">
                <Link to="/features" className="flex items-center gap-2">
                  Explore All Features
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </Link>
              </Button>
            </div>
          </ScrollReveal>
        </div>
      </section>

      {/* App Screenshots */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="section-title text-center">App Preview</h2>
          <p className="section-subtitle text-center">
            See how Sadaad helps you manage emotions and grow spiritually
          </p>

          <div className="app-preview-container pb-8 pt-8 px-4 overflow-hidden relative">
            <div className="app-preview-scroll flex gap-6">
              {/* First set of images */}
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={emotionControle}
                  alt="Emotion Control Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Emotion Control</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={chatbot}
                  alt="Islamic Chatbot Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Islamic Chatbot</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={quiz}
                  alt="Daily Quiz Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Daily Quiz</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={bookmark}
                  alt="Bookmarks Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Bookmarks</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={dua}
                  alt="Daily Dua Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Daily Dua</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={emotionOverview}
                  alt="Emotion Journey Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Emotion Journey</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={quizHistory}
                  alt="Quiz History Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Quiz History</h3>
              </div>

              {/* Duplicate set for seamless looping */}
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={emotionControle}
                  alt="Emotion Control Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Emotion Control</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={chatbot}
                  alt="Islamic Chatbot Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Islamic Chatbot</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={quiz}
                  alt="Daily Quiz Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Daily Quiz</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={bookmark}
                  alt="Bookmarks Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Bookmarks</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={dua}
                  alt="Daily Dua Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Daily Dua</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={emotionOverview}
                  alt="Emotion Journey Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Emotion Journey</h3>
              </div>
              <div className="flex-shrink-0 w-[280px] app-preview-item mx-3">
                <img
                  src={quizHistory}
                  alt="Quiz History Screen"
                  className="app-screenshot w-full h-auto"
                />
                <h3 className="app-preview-title">Quiz History</h3>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="section-title text-center">What Users Say</h2>
          <p className="section-subtitle text-center">
            Hear from Muslims who have benefited from Sadaad
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
            <div className="bg-white rounded-2xl shadow p-6 border border-gray-100">
              <div className="flex items-center space-x-1 mb-4">
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
              </div>
              <p className="text-gray-600 mb-4">
                "Sadaad has helped me manage my anger issues through Qur'anic guidance. The instant emotional control feature is what I needed."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold">A</div>
                <div className="ml-3">
                  <h4 className="font-bold text-gray-800">Ahmed</h4>
                  <p className="text-sm text-gray-500">Student, 19</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow p-6 border border-gray-100">
              <div className="flex items-center space-x-1 mb-4">
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
              </div>
              <p className="text-gray-600 mb-4">
                "The chatbot answers my questions with authentic sources. I've learned so much about managing anxiety through Islamic teachings."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-secondary/20 rounded-full flex items-center justify-center text-secondary font-bold">F</div>
                <div className="ml-3">
                  <h4 className="font-bold text-gray-800">Fatima</h4>
                  <p className="text-sm text-gray-500">Professional, 27</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow p-6 border border-gray-100">
              <div className="flex items-center space-x-1 mb-4">
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                <Star className="w-5 h-5 text-yellow-500" />
              </div>
              <p className="text-gray-600 mb-4">
                "Daily quizzes keep me engaged with my deen and the bookmark feature helps me save important advice for later review."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-accent rounded-full flex items-center justify-center text-primary font-bold">Y</div>
                <div className="ml-3">
                  <h4 className="font-bold text-gray-800">Yusuf</h4>
                  <p className="text-sm text-gray-500">Teacher, 32</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Download CTA */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-primary/10 to-secondary/10">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="section-title">Download Sadaad Today</h2>
            <p className="section-subtitle">
              Take the first step towards emotional well-being guided by Islamic wisdom
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center mt-8">
              <Button asChild size="lg" className="rounded-full">
                <a href="#">
                  <svg className="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8 12L11 15L16 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Google Play
                </a>
              </Button>
              <Button asChild variant="outline" size="lg" className="rounded-full">
                <a href="#">
                  <svg className="w-6 h-6 mr-2" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M15 12H12V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  App Store
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Index;
