
import React from "react";

const Privacy = () => {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-8 text-gray-800">Privacy Policy</h1>

        <div className="prose max-w-none">
          <p className="text-sm text-gray-500 mb-6">Last Updated: May 2024</p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Introduction</h2>
          <p className="mb-6">
            Welcome to Sadaad ("we," "our," or "us"). We care about your privacy and want to be clear about how we handle your information. This Privacy Policy explains what information we collect, how we use it, and your rights when you use our app.
          </p>
          <p className="mb-6">
            By using our app, you agree to these terms. If you don't agree, please don't use our app.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Information We Collect</h2>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">Personal Information</h3>
          <p className="mb-4">When you use our app, we collect:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Your email address, name, and username when you create an account</li>
            <li>Basic profile information if you sign in with Google</li>
            <li>Your selected profile picture from our options</li>
          </ul>

          <h3 className="text-xl font-bold mt-6 mb-2 text-gray-800">App Usage Information</h3>
          <p className="mb-4">We store the following information in our database:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Your emotion tracking data and progress</li>
            <li>Your progress with duas and quizzes</li>
            <li>Messages you bookmark</li>
            <li>Your quiz history and scores</li>
          </ul>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">How We Use Your Information</h2>
          <p className="mb-4">We use the information stored in our database to:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Provide the app features you expect</li>
            <li>Personalize your experience</li>
            <li>Track and display your progress</li>
            <li>Improve the app based on usage patterns</li>
            <li>Provide customer support</li>
          </ul>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">How We Protect Your Data</h2>
          <p className="mb-6">
            We take security seriously and use industry-standard measures to protect your information. We keep your data only as long as needed to provide our services. If you delete your account, we'll delete or anonymize your information within a reasonable time, unless we're legally required to keep it.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Third-Party Services</h2>
          <p className="mb-6">
            We work with these trusted partners:
          </p>
          <ul className="list-disc ml-6 mb-4">
            <li><strong>Google:</strong> For sign-in and AI chatbot technology</li>
            <li><strong>Razorpay:</strong> For processing subscription payments</li>
            <li><strong>Analytics services:</strong> To help us improve the app</li>
          </ul>
          <p className="mb-6">
            Each of these companies has their own privacy policy. We encourage you to review their policies if you want more information.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Your Rights</h2>
          <p className="mb-4">You have the right to:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Access and update your personal information through the app settings</li>
            <li>Request a copy of your data</li>
            <li>Delete your account and associated information</li>
            <li>Object to how we use your data</li>
            <li>Withdraw your consent at any time</li>
          </ul>
          <p className="mb-6">
            To exercise these rights, please contact us using the email address at the end of this policy.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Children's Privacy</h2>
          <p className="mb-6">
            Our app is not for children under 13 years old. We don't knowingly collect information from children. If you're a parent and believe your child has given us their information, please contact us, and we'll delete it.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Changes to This Privacy Policy</h2>
          <p className="mb-6">
            We may update this policy occasionally. When we do, we'll let you know by updating the "Last Updated" date at the top of this page. We recommend checking back periodically.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Legal Compliance</h2>
          <p className="mb-6">
            We comply with privacy laws including GDPR (for European users) and CCPA (for California residents). We don't sell your personal information.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Contact Us</h2>
          <p className="mb-6">
            Questions about your privacy? Contact us at:
          </p>
          <p className="mb-4">Email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
        </div>
      </div>
    </div>
  );
};

export default Privacy;
