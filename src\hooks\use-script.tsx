import { useEffect, useState } from 'react';

export default function useScript(src: string) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let script = document.querySelector(`script[src="${src}"]`) as HTMLScriptElement;

    if (!script) {
      script = document.createElement('script');
      script.src = src;
      script.async = true;

      const handleLoad = () => {
        setLoading(false);
      };

      const handleError = (error: any) => {
        setError(error);
        setLoading(false);
      };

      script.addEventListener('load', handleLoad);
      script.addEventListener('error', handleError);

      document.body.appendChild(script);

      return () => {
        script.removeEventListener('load', handleLoad);
        script.removeEventListener('error', handleError);
      };
    } else {
      setLoading(false);
    }
  }, [src]);

  return [loading, error];
}
