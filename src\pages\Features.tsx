
import {
  MessageS<PERSON>re,
  BookMarked,
  Calendar,
  Star,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  BookOpen,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// Import emotion control images
import emotionControlHome from "../assets/images/features/emotion-controle/home.png";
import emotionControlDua from "../assets/images/features/emotion-controle/dua.png";
import emotionControlEdit from "../assets/images/features/emotion-controle/edit.png";

// Import chatbot images
import chatbotImage from "../assets/images/features/chatbot/chatbot.png";
import bookmarkImage from "../assets/images/features/chatbot/bookmark.png";

// Import daily quiz images
import quizImage from "../assets/images/features/daily-quiz/quiz.png";
import resultImage from "../assets/images/features/daily-quiz/result.png";
import historyImage from "../assets/images/features/daily-quiz/history.png";

// Import bookmark messages images
import bookmarkFeatureImage from "../assets/images/features/bookmark/bookmark.png";
import categoriesImage from "../assets/images/features/bookmark/catagories.png";

// Import daily dua images
import duaImage from "../assets/images/features/daily-dua/dua.png";
import progressImage from "../assets/images/features/daily-dua/progress.png";
import achievementImage from "../assets/images/features/daily-dua/achivement.png";

// Import emotion journey images
import emotionHomeImage from "../assets/images/features/emotion-journey/home.png";
import emotionDateImage from "../assets/images/features/emotion-journey/date.png";
import emotionJourneyImage from "../assets/images/features/emotion-journey/emotion-journey.png";
import emotionCountsImage from "../assets/images/features/emotion-journey/counts.png";

const Features = () => {
  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/10 to-secondary/10 py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Features Designed for Emotional Wellbeing
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              Discover how Sadaad combines Islamic teachings with modern technology to help you master your emotions
            </p>
          </div>
        </div>
      </section>

      {/* Feature Sections */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          {/* Feature 1 - Instant Emotional Control */}
          <div className="feature-container mb-24">
            <div className="feature-frames">
              {/* Frame 1/3 */}
              <div className="feature-frame active flex flex-col md:flex-row items-center" id="emotional-control-1">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-primary/10 inline-block rounded-2xl p-3 mb-4">
                    <MessageSquare className="h-8 w-8 text-primary" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Name What You're Feeling
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Feeling overwhelmed? Our friendly interface helps you identify exactly what's going on in your heart, making it easier to find the perfect Islamic guidance.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Simply tap what you're feeling - anger, fear, sadness, or any emotion troubling you</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Choose what you need: understanding why you feel this way or practical steps to feel better</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Need help right now? The "Instant Control" button is always there for you</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionControlHome}
                    alt="Name What You're Feeling"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 2/3 */}
              <div className="feature-frame flex flex-col md:flex-row items-center" id="emotional-control-2">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-primary/10 inline-block rounded-2xl p-3 mb-4">
                    <MessageSquare className="h-8 w-8 text-primary" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Find Peace in Allah's Words
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    When emotions feel heavy, we'll lift your heart with beautiful guidance from the Quran and Sunnah that speaks directly to what you're going through.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Discover comforting Quranic verses that feel like they were revealed just for your situation</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Find wisdom in the Prophet's ﷺ words that shows you're not alone in what you're feeling</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Soothe your heart with beautiful duas that bring immediate spiritual comfort</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Try our simple three-step plan to feel better: a prayer to say, an action to take, and a change to make</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionControlDua}
                    alt="Find Peace in Allah's Words"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 3/3 */}
              <div className="feature-frame flex flex-col md:flex-row items-center" id="emotional-control-3">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-primary/10 inline-block rounded-2xl p-3 mb-4">
                    <MessageSquare className="h-8 w-8 text-primary" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Make It Yours
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    We all face different emotional challenges. Customize your Sadaad experience to focus on what matters most in your personal journey to emotional wellbeing.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Keep your most common emotional challenges right on your home screen for quick access</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Add new emotions that are unique to your personal struggles</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Hide emotions that aren't relevant to your life right now</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-primary mr-2 mt-0.5" />
                      <span className="text-gray-700">Put your biggest challenges front and center for those moments when you need help most</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionControlEdit}
                    alt="Make It Yours"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

            </div>

            {/* Navigation */}
            <div className="frame-navigation">
              <button className="frame-arrow" aria-label="Previous frame">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <div className="frame-dot active" data-frame="emotional-control-1"></div>
                <div className="frame-dot" data-frame="emotional-control-2"></div>
                <div className="frame-dot" data-frame="emotional-control-3"></div>
              </div>

              <button className="frame-arrow" aria-label="Next frame">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Feature 2 - Islamic Chatbot */}
          <div className="feature-container mb-24">
            <div className="feature-frames">
              {/* Frame 1/2 */}
              <div className="feature-frame active flex flex-col md:flex-row-reverse items-center" id="islamic-chatbot-1">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-secondary/10 inline-block rounded-2xl p-3 mb-4">
                    <MessageSquare className="h-8 w-8 text-secondary" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Your Personal Islamic Guide
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Have a question about Islam? Just ask Sadaad, your friendly AI companion who's always ready with reliable, authentic answers from Islamic sources.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Get trustworthy answers from the Quran and authentic hadith</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Know exactly where each answer comes from with helpful references</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Enjoy respectful conversations with proper Islamic etiquette</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Ask about any Islamic topic and get clear, helpful guidance</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={chatbotImage}
                    alt="Your Personal Islamic Guide"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 2/2 */}
              <div className="feature-frame flex flex-col md:flex-row-reverse items-center" id="islamic-chatbot-2">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-secondary/10 inline-block rounded-2xl p-3 mb-4">
                    <MessageSquare className="h-8 w-8 text-secondary" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Save What Matters to You
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Found an answer that touched your heart? Save it for later with a simple tap and build your own personal collection of Islamic wisdom.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Save your favorite answers with just one tap</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Keep everything neatly organized by topics you care about</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Find all your saved wisdom in one special place</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-secondary mr-2 mt-0.5" />
                      <span className="text-gray-700">Share beautiful Islamic insights with people you love</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={bookmarkImage}
                    alt="Save What Matters to You"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>


            </div>

            {/* Navigation */}
            <div className="frame-navigation">
              <button className="frame-arrow" aria-label="Previous frame">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <div className="frame-dot active" data-frame="islamic-chatbot-1"></div>
                <div className="frame-dot" data-frame="islamic-chatbot-2"></div>
              </div>

              <button className="frame-arrow" aria-label="Next frame">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Feature 3 - Daily Quiz */}
          <div className="feature-container mb-24">
            <div className="feature-frames">
              {/* Frame 1/3 */}
              <div className="feature-frame active flex flex-col md:flex-row items-center" id="daily-quiz-1">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-yellow-100 inline-block rounded-2xl p-3 mb-4">
                    <Calendar className="h-8 w-8 text-yellow-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Grow Your Faith Daily
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Start each day with a fun mini-quiz that helps you discover fascinating aspects of Islam you might not know yet - it's learning that never feels like studying!
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Enjoy 10 fresh questions every morning to brighten your day with knowledge</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Explore beautiful stories about the Prophets, hidden meanings in the Quran, and Islamic history</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Answer thoughtful multiple-choice questions that make you think, not just memorize</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={quizImage}
                    alt="Grow Your Faith Daily"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 2/3 */}
              <div className="feature-frame flex flex-col md:flex-row items-center" id="daily-quiz-2">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-yellow-100 inline-block rounded-2xl p-3 mb-4">
                    <Calendar className="h-8 w-8 text-yellow-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Learn With Every Answer
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Whether you get a question right or wrong, you'll always walk away with new wisdom and understanding about our beautiful faith.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Hear a gentle sound that lets you know right away if your answer was correct</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Discover the "why" behind each answer with fascinating explanations that stick with you</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Enjoy a delightful celebration when you do well - your knowledge deserves recognition!</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={resultImage}
                    alt="Learn With Every Answer"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 3/3 */}
              <div className="feature-frame flex flex-col md:flex-row items-center" id="daily-quiz-3">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-yellow-100 inline-block rounded-2xl p-3 mb-4">
                    <Calendar className="h-8 w-8 text-yellow-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    See How Far You've Come
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Look back at your beautiful journey of Islamic learning and feel proud of every bit of knowledge you've gained along the way.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Revisit every quiz you've taken and see how your knowledge has blossomed</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">View your learning journey on a beautiful calendar that shows your dedication</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Dive into any past quiz to remember what you learned that day</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">See which questions you answered correctly and which ones taught you something new</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={historyImage}
                    alt="See How Far You've Come"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="frame-navigation">
              <button className="frame-arrow" aria-label="Previous frame">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <div className="frame-dot active" data-frame="daily-quiz-1"></div>
                <div className="frame-dot" data-frame="daily-quiz-2"></div>
                <div className="frame-dot" data-frame="daily-quiz-3"></div>
              </div>

              <button className="frame-arrow" aria-label="Next frame">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Feature 4 - Bookmark Messages */}
          <div className="feature-container mb-24">
            <div className="feature-frames">
              {/* Frame 1/2 */}
              <div className="feature-frame active flex flex-col md:flex-row-reverse items-center" id="bookmarks-1">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-purple-100 inline-block rounded-2xl p-3 mb-4">
                    <BookMarked className="h-8 w-8 text-purple-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Keep Wisdom Close to Heart
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Found something that touched your soul? Save those precious gems of Islamic wisdom with a simple tap so they're always there when you need them most.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">When Sadaad shares something that moves your heart, save it with just one tap</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Easily find that perfect hadith or verse again when you need its comfort</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Enjoy your saved wisdom in a peaceful, beautiful reading space</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Brighten someone else's day by sharing beautiful Islamic insights</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={bookmarkFeatureImage}
                    alt="Keep Wisdom Close to Heart"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 2/2 */}
              <div className="feature-frame flex flex-col md:flex-row-reverse items-center" id="bookmarks-2">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-purple-100 inline-block rounded-2xl p-3 mb-4">
                    <BookMarked className="h-8 w-8 text-purple-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Your Personal Wisdom Library
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Watch your own beautiful collection of Islamic knowledge grow and bloom as you organize it in ways that make sense for your unique spiritual journey.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Explore your saved wisdom by topics that matter to you</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Quickly find Quranic verses, duas, or hadith when your heart needs them</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Create your own personal treasure chest of Islamic wisdom, organized your way</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Focus on exactly what you need right now - whether it's all your saved wisdom or just one area</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={categoriesImage}
                    alt="Your Personal Wisdom Library"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>


            </div>

            {/* Navigation */}
            <div className="frame-navigation">
              <button className="frame-arrow" aria-label="Previous frame">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <div className="frame-dot active" data-frame="bookmarks-1"></div>
                <div className="frame-dot" data-frame="bookmarks-2"></div>
              </div>

              <button className="frame-arrow" aria-label="Next frame">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Feature 5 - Daily Dua */}
          <div className="feature-container mb-24">
            <div className="feature-frames">
              {/* Frame 1/3 */}
              <div className="feature-frame active flex flex-col md:flex-row items-center" id="daily-dua-1">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-green-100 inline-block rounded-2xl p-3 mb-4">
                    <BookOpen className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    A Year of Beautiful Prayers
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Begin each day with a new dua that brings you closer to Allah, creating a beautiful daily ritual that nourishes your soul all year long.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Wake up to a new heartfelt prayer every morning for a full year of spiritual growth</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Learn each dua in beautiful Arabic with easy-to-read transliteration and meaning</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Discover the perfect moments in your day to use each special prayer</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Keep your duas with you everywhere - even without internet connection</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={duaImage}
                    alt="A Year of Beautiful Prayers"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 2/3 */}
              <div className="feature-frame flex flex-col md:flex-row items-center" id="daily-dua-2">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-green-100 inline-block rounded-2xl p-3 mb-4">
                    <BookOpen className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Watch Your Faith Grow
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Feel the joy of seeing your dedication bloom day by day as you build a beautiful habit that strengthens your connection with Allah.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Feel proud as you watch your streak of daily prayers grow longer</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">See how far you've come on your year-long journey of beautiful duas</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Receive heartwarming messages that celebrate your dedication</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Never worry about missing a day - your journey continues right where you left off</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={progressImage}
                    alt="Watch Your Faith Grow"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 3/3 */}
              <div className="feature-frame flex flex-col md:flex-row items-center" id="daily-dua-3">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                  <div className="bg-green-100 inline-block rounded-2xl p-3 mb-4">
                    <BookOpen className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Your Beautiful Prayer Map
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    See your spiritual journey come to life with a stunning calendar that shows every step you've taken on your path of daily connection with Allah.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Enjoy a bird's-eye view of your entire year of duas on one beautiful screen</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Feel a sense of achievement seeing each completed day bloom with a green checkmark</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Easily find today's special dua glowing in warm amber light</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Draw inspiration from watching your calendar fill with more and more completed prayers</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={achievementImage}
                    alt="Your Beautiful Prayer Map"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="frame-navigation">
              <button className="frame-arrow" aria-label="Previous frame">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <div className="frame-dot active" data-frame="daily-dua-1"></div>
                <div className="frame-dot" data-frame="daily-dua-2"></div>
                <div className="frame-dot" data-frame="daily-dua-3"></div>
              </div>

              <button className="frame-arrow" aria-label="Next frame">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Feature 6 - Emotion Journey */}
          <div className="feature-container">
            <div className="feature-frames">
              {/* Frame 1/4 */}
              <div className="feature-frame active flex flex-col md:flex-row-reverse items-center" id="emotion-journey-1">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-blue-100 inline-block rounded-2xl p-3 mb-4">
                    <PieChart className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Understand Your Heart
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Get to know yourself better by gently tracking your emotions, helping you recognize patterns and grow in self-awareness in a way that feels natural and supportive.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Notice what your heart is feeling with our simple, caring emotion tracker</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Discover which emotions visit you most often and understand their messages</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Celebrate each time you successfully navigate through emotional storms</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Receive kind, supportive words tailored to your unique emotional journey</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionHomeImage}
                    alt="Understand Your Heart"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 2/4 */}
              <div className="feature-frame flex flex-col md:flex-row-reverse items-center" id="emotion-journey-2">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-blue-100 inline-block rounded-2xl p-3 mb-4">
                    <PieChart className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    See How Far You've Come
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Look back at your emotional journey with gentle eyes and celebrate how much you've grown, learned, and healed along the way.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Travel back through time to see your emotional story unfold month by month</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Notice how your emotional landscape changes with the seasons of your life</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Discover what situations tend to trigger certain feelings in your heart</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Feel proud seeing how emotions that once overwhelmed you now feel more manageable</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionDateImage}
                    alt="See How Far You've Come"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 3/4 */}
              <div className="feature-frame flex flex-col md:flex-row-reverse items-center" id="emotion-journey-3">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-blue-100 inline-block rounded-2xl p-3 mb-4">
                    <PieChart className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    See Your Heart in Color
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Watch your emotional world come alive through beautiful, colorful charts that help you understand your feelings in a way that words alone never could.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Explore your emotions through vibrant, interactive charts that respond to your touch</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">See at a glance which emotions have been coloring your days lately</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Receive gentle insights that help you understand what your heart is trying to tell you</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Discover areas where Allah might be inviting you to grow and heal</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionJourneyImage}
                    alt="See Your Heart in Color"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>

              {/* Frame 4/4 */}
              <div className="feature-frame flex flex-col md:flex-row-reverse items-center" id="emotion-journey-4">
                <div className="md:w-1/2 mb-8 md:mb-0 md:pl-8">
                  <div className="bg-blue-100 inline-block rounded-2xl p-3 mb-4">
                    <PieChart className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Your Complete Emotional Story
                  </h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Explore the full tapestry of your emotional life with detailed insights and gentle Islamic wisdom tailored specifically to your heart's unique journey.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Browse through your complete emotional journey like pages in your personal story</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">See exactly how many times each emotion has visited your heart</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Receive thoughtful reflections about the emotions that appear most in your life</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <span className="text-gray-700">Find comfort in Islamic wisdom chosen especially for your emotional patterns</span>
                    </li>
                  </ul>
                </div>
                <div className="md:w-1/2 flex justify-center">
                  <img
                    src={emotionCountsImage}
                    alt="Your Complete Emotional Story"
                    className="app-screenshot w-[250px] md:w-[300px] h-auto rounded-3xl"
                  />
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="frame-navigation">
              <button className="frame-arrow" aria-label="Previous frame">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>

              <div className="flex gap-2">
                <div className="frame-dot active" data-frame="emotion-journey-1"></div>
                <div className="frame-dot" data-frame="emotion-journey-2"></div>
                <div className="frame-dot" data-frame="emotion-journey-3"></div>
                <div className="frame-dot" data-frame="emotion-journey-4"></div>
              </div>

              <button className="frame-arrow" aria-label="Next frame">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon Features */}
      <section className="py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="section-title">Coming Soon</h2>
            <p className="section-subtitle">
              We're constantly improving Sadaad with new features to enhance your spiritual journey
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
              <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
                <div className="bg-secondary/10 inline-block rounded-xl p-2 mb-3">
                  <Star className="h-6 w-6 text-secondary" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">Community Support</h3>
                <p className="text-gray-600">
                  Connect with other Muslims on their spiritual journey for mutual support and encouragement.
                </p>
              </div>

              <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
                <div className="bg-primary/10 inline-block rounded-xl p-2 mb-3">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">Habit Tracker</h3>
                <p className="text-gray-600">
                  Build positive Islamic habits with reminders and progress tracking for daily practices.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Features;