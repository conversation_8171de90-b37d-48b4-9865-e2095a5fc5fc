import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Check } from "lucide-react";
import useScript from "@/hooks/use-script";

const Pricing = () => {
  const [loading] = useScript("https://checkout.razorpay.com/v1/checkout.js");

  const handlePayment = (plan: any) => {
    if (loading) {
      // Script is still loading, handle appropriately
      return;
    }

    const amountInPaisa = parseInt(plan.price.replace("₹", "")) * 100;

    const options = {
      key: "rzp_test_0HFTDTBdIwMYMN", // Replace with your Razorpay Key ID
      amount: amountInPaisa,
      currency: "INR",
      name: "<PERSON>aa<PERSON>",
      description: `Payment for ${plan.name}`,
      // image: "https://your_logo.png",
      // order_id: "order_9A33XG40g", // Replace with the order_id generated from your backend
      handler: function (response: any) {
        // Handle payment success
        // You should verify the payment signature on your backend
        alert(`Payment successful. Payment ID: ${response.razorpay_payment_id}`);
      },
      prefill: {
        name: "Your Name",
        email: "<EMAIL>",
        contact: "9999999999",
      },
      notes: {
        address: "Sadaad Corporate Office",
      },
      theme: {
        color: "#3399cc",
      },
    };

    const rzp = new (window as any).Razorpay(options);

    rzp.on("payment.failed", function (response: any) {
      // Handle payment failure
      alert(`Payment failed. Error: ${response.error.description}`);
    });

    rzp.open();
  };

  const plans = [
    {
      name: "Free Plan",
      price: "Free",
      description: "Your Free Plan Includes:",
      features: [
        "25 Messages per month",
        "5 Daily Quizzes per month",
        "5 Bookmarks maximum",
        "Basic AI Model",
        "No Quiz History Access",
        "No Customizable Emotions Tracking"
      ],
      buttonText: "Get Started",
      buttonVariant: "outline" as const,
      note: "Upgrade to premium for unlimited daily quizzes, unlimited messages, customizable emotions tracking, quiz history access, and more advanced features."
    },
    {
      name: "Sabr (صبر) Plan",
      price: "₹99",
      period: "month",
      description: "Unlimited access with advanced features. Sabr means patience, an essential virtue in Islam. This plan represents consistent growth and perseverance in gaining Islamic knowledge.",
      features: [
        "Unlimited Messages",
        "Advanced AI Model",
        "Unlimited Bookmarks",
        "Unlimited Daily Quizzes",
        "Quiz History Access",
        "Customizable Emotions Tracking"
      ],
      buttonText: "Choose Sabr (صبر) Plan",
      buttonVariant: "default" as const
    },
    {
      name: "Ikhlas (إخلاص) Plan",
      price: "₹999",
      period: "year",
      originalPrice: "₹1188",
      savings: "Save 16%",
      description: "Unlimited access with maximum savings. Ikhlas stands for sincerity and purity of intention, reflecting a long-term commitment to learning and practicing Islam.",
      features: [
        "Unlimited Messages",
        "Advanced AI Model",
        "Unlimited Bookmarks",
        "Unlimited Daily Quizzes",
        "Quiz History Access",
        "Customizable Emotions Tracking"
      ],
      buttonText: "Choose Ikhlas (إخلاص) Plan",
      buttonVariant: "default" as const,
      popular: true,
      badge: "BEST VALUE",
      limitedOffer: "Limited time offer - Save ₹189 today!"
    }
  ];
  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Simple, transparent pricing
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Choose the perfect plan for your journey to emotional well-being with Sadaad
          </p>
        </div>
        <div className="isolate mx-auto mt-16 grid max-w-6xl grid-cols-1 gap-y-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-x-8 xl:gap-x-12">
          {plans.map((plan) => (
            <Card key={plan.name} className={`p-8 ${plan.popular ? 'ring-2 ring-primary' : 'ring-1 ring-gray-200'} relative`}>
              <div className="flex flex-col h-full">
                <div>
                  {plan.badge && (
                    <div className="absolute -top-3 right-4 inline-flex rounded-full px-4 py-1 text-xs font-semibold bg-green-600 text-white">
                      {plan.badge}
                    </div>
                  )}
                  {plan.popular && !plan.badge && (
                    <div className="inline-flex rounded-full px-4 py-1 text-xs font-semibold bg-primary/10 text-primary mb-4">
                      Most Popular
                    </div>
                  )}
                  <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-4xl font-bold tracking-tight text-gray-900">{plan.price}</span>
                    {plan.period && (
                      <span className="text-gray-600 ml-1">/{plan.period}</span>
                    )}
                    {plan.originalPrice && (
                      <span className="text-lg text-gray-400 line-through ml-2">{plan.originalPrice}</span>
                    )}
                  </div>
                  {plan.savings && (
                    <div className="mt-2 text-sm font-medium text-green-600">{plan.savings}</div>
                  )}
                  {plan.limitedOffer && (
                    <div className="mt-2 text-sm font-medium text-orange-600 italic">{plan.limitedOffer}</div>
                  )}
                  <p className="mt-2 text-base text-gray-600">{plan.description}</p>
                </div>

                <div className="mt-8 space-y-4 flex-1">
                  {plan.features.map((feature) => (
                    <div key={feature} className="flex gap-x-3">
                      {feature.startsWith("No ") ? (
                        <span className="h-6 w-5 flex-none text-red-500 text-lg">✗</span>
                      ) : (
                        <Check className="h-6 w-5 flex-none text-primary" />
                      )}
                      <span className={`text-gray-600 ${feature.startsWith("No ") ? "text-red-500 italic" : ""}`}>
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>

                {plan.note && (
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-700 italic">{plan.note}</p>
                  </div>
                )}

                <Button
                  variant={plan.buttonVariant}
                  className="mt-8 w-full"
                  onClick={() => plan.price !== "Free" && handlePayment(plan)}
                  disabled={plan.price === "Free"}
                >
                  {plan.buttonText}
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Pricing;
