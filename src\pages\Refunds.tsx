import React from "react";

const Refunds = () => {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-8 text-gray-800">Refund Policy</h1>

        <div className="prose max-w-none">
          <p className="text-sm text-gray-500 mb-6">Effective Date: August 6, 2025</p>

          <p className="mb-6">
            Thank you for choosing our services. Please read this policy carefully before making a payment.
          </p>
          <p className="mb-6">
            We currently do not offer refunds for any purchases made on our platform, including but not limited to digital products, subscriptions, or services.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Why No Refunds?</h2>
          <p className="mb-6">
            Due to the nature of our offerings — which are delivered immediately and involve digital assets or services — we are unable to provide refunds once a transaction is completed.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Exceptions</h2>
          <p className="mb-4">We only consider refunds under the following exceptional circumstances:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>You were charged more than once for the same transaction.</li>
            <li>A payment was made fraudulently or in error (with valid proof).</li>
          </ul>
          <p className="mb-6">
            In such cases, please contact us within 3 working days at:
          </p>
          <p className="mb-4">📩 <EMAIL></p>
          <p className="mb-4">📞 9847509916</p>
        </div>
      </div>
    </div>
  );
};

export default Refunds;
