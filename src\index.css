
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 142 76% 36%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 200 38% 29%;  /* #2e5266 */
    --secondary-foreground: 355.7 100% 97.3%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 142 70% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;

    --radius: 1rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 70% 50%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 200 38% 29%;  /* #2e5266 */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 142 70% 20%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-poppins;
  }
}

.hero-gradient {
  background: linear-gradient(180deg, rgba(240, 255, 244, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
  position: relative;
}

.hero-gradient::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(46, 82, 102, 0.08) 0%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
}

/* Modern Feature Cards */
.features-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-12;
}

.feature-card {
  @apply bg-white rounded-2xl p-6 transition-all duration-500 border border-gray-100 relative overflow-hidden;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05);
  transform: translateY(0);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(var(--accent), 0.2) 0%, rgba(var(--accent), 0) 60%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 30px -10px rgba(0, 0, 0, 0.1);
  border-color: rgba(var(--primary), 0.2);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card:focus-within {
  @apply ring-2 ring-primary/50 outline-none;
}

.feature-card h3 {
  @apply text-xl font-bold text-gray-800 relative z-10;
  margin-bottom: 0.75rem;
  transition: transform 0.3s ease, color 0.3s ease;
}

.feature-card:hover h3 {
  @apply text-primary;
  transform: translateX(4px);
}

.feature-card p {
  @apply text-gray-600 relative z-10;
  margin-top: auto;
  transition: transform 0.3s ease;
}

.feature-card:hover p {
  transform: translateX(4px);
}

.feature-icon-wrapper {
  @apply relative z-10 mb-5;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.feature-card:hover .feature-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.feature-icon {
  @apply bg-accent rounded-full p-3 w-14 h-14 flex items-center justify-center text-primary;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(var(--primary), 0.2) 0%, rgba(var(--primary), 0) 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.feature-card:hover .feature-icon {
  @apply bg-primary text-white;
}

.feature-card:hover .feature-icon::after {
  opacity: 1;
}

/* Staggered animation for feature cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

/* Delay classes for staggered animations */
.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }
.delay-500 { transition-delay: 0.5s; }
.delay-600 { transition-delay: 0.6s; }
.delay-700 { transition-delay: 0.7s; }

/* Scroll Reveal Animation */
.scroll-reveal-element {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.scroll-reveal-element.is-revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Add a subtle pulse animation to the feature icons */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.feature-icon-pulse {
  animation: pulse 3s ease-in-out infinite;
}

.cta-button-primary {
  @apply bg-primary text-white rounded-full px-6 py-3 font-medium transition-all hover:bg-primary/90 shadow-sm hover:shadow-md relative overflow-hidden;
}

.cta-button-primary::after,
.button-shine::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.cta-button-primary:hover::after,
.button-shine:hover::after {
  animation: shine 1.5s ease;
}

@keyframes shine {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(30deg);
  }
  20% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: translateX(100%) rotate(30deg);
  }
}

.cta-button-secondary {
  @apply bg-white text-primary border border-primary rounded-full px-6 py-3 font-medium transition-all hover:bg-accent shadow-sm hover:shadow-md;
}

.section-title {
  @apply text-3xl md:text-4xl font-bold mb-4 text-gray-800;
}

.section-subtitle {
  @apply text-lg text-gray-600 mb-8 max-w-2xl mx-auto;
}

.app-screenshot {
  @apply rounded-3xl shadow-lg;
}

.app-preview-item {
  @apply flex flex-col items-center;
}

.app-preview-title {
  @apply text-lg font-medium text-gray-800 mt-4 text-center;
}

/* Hide scrollbar but keep functionality */
.app-preview-container {
  @apply overflow-x-auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.app-preview-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Animation for horizontal scrolling */
@keyframes scrollHorizontally {
  0% { transform: translateX(0); }
  100% { transform: translateX(calc(-280px * 7 - 1.5rem * 7)); } /* Move exactly the width of the first 7 items */
}

.app-preview-scroll {
  display: flex;
  animation: scrollHorizontally 40s linear infinite;
  width: fit-content;
  padding: 0.5rem 0;
}

.app-preview-scroll:hover {
  animation-play-state: paused;
}

/* Make sure we have enough items for a seamless loop */
.app-preview-scroll {
  display: flex;
  width: calc(280px * 14 + 1.5rem * 14); /* Double the content for seamless loop */
}

/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Feature frames for navigation */
.feature-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.feature-frames {
  position: relative;
}

.feature-frame {
  width: 100%;
  display: none;
  transition: all 0.3s ease;
}

.feature-frame.active {
  display: flex;
}

/* Ensure proper layout for feature frames */
@media (max-width: 767px) {
  .feature-frame {
    flex-direction: column;
  }
}

@media (min-width: 768px) {
  .feature-frame {
    flex-direction: row;
  }

  .feature-frame.md\:flex-row-reverse {
    flex-direction: row-reverse;
  }
}

.frame-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
}

.frame-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.frame-dot.active {
  background-color: #3b82f6;
  transform: scale(1.25);
}

.frame-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.frame-arrow:hover {
  background-color: #f8fafc;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Word Rotation Animation */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

.word-rotate-wrapper {
  display: inline-block;
  position: relative;
}

.word-rotate-item {
  display: inline-block;
  position: absolute;
  opacity: 0;
  overflow: hidden;
  animation: fadeOut 0.5s ease forwards;
}

.word-rotate-item.active {
  opacity: 1;
  position: relative;
  animation: fadeIn 0.5s ease forwards;
}
