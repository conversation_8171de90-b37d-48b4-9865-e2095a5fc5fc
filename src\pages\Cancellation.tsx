import React from "react";

const Cancellation = () => {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-8 text-gray-800">Cancellation Policy</h1>

        <div className="prose max-w-none">
          <p className="text-sm text-gray-500 mb-6">Effective Date: August 6, 2025</p>

          <p className="mb-6">
            Please note that we do not accept cancellations once an order, service, or subscription is confirmed and payment has been processed.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Why No Cancellations?</h2>
          <p className="mb-6">
            Our services are delivered instantly or begin processing immediately upon confirmation, which makes cancellations impractical.
          </p>

          <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-800">Need Help?</h2>
          <p className="mb-4">If you experience issues or have questions, we’re here to help. Please contact our support team:</p>
          <p className="mb-4">📩 <EMAIL></p>
          <p className="mb-4">📞 9847509916</p>
        </div>
      </div>
    </div>
  );
};

export default Cancellation;
